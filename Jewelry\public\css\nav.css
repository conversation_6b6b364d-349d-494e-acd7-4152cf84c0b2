:root {
    --white: #fff;
    --black: #222;
    --green: #C9B194;
    --grey1: #f0f0f0;
    --grey2: #f1f1f1;
  }
/* 
=================
Navigation
=================
*/
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #d1d1d1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
}


.navigation {
    height: 10rem;
    line-height: 6rem;
  }
  
  .nav-center {
    justify-content: space-between;
  }
  
  .nav-list .icons {
    display: none  ;
  }
  
  .nav-center .nav-item:not(:last-child) {
    margin-right: 0.5rem;
  }
  
  .nav-center .nav-link {
    font-size: 1.8rem;
    padding: 1rem;
  }
  
  .nav-center .nav-link:hover {
    color: var(--green);
    
  }
  
  .nav-center .hamburger {
    display: none;
    font-size: 2.3rem;
    color: var(--black);
    cursor: pointer;
  }
  
  
  .navigation:hover{
    background-color: white;
    transition: 0.3s ease-in-out;
    -webkit-transition: 0.3s ease-in-out;
    -moz-transition: 0.3s ease-in-out;
    -ms-transition: 0.3s ease-in-out;
    -o-transition: 0.3s ease-in-out;
    
  }
  
  .nav-list .nav-link {
    font-family: 'Inter', sans-serif;
    font-weight: 500;
    font-size: 1.5rem;
    padding: 1.7rem; 
    
  }
  /* Icons */
  
  .icon {
    cursor: pointer;
    font-size: 2.5rem;
    padding: 0 1rem;
    color: #555;
    position: relative;
  }
  
  .icon:not(:last-child) {
    margin-right: 0.5rem;
  }
  
  .icon span , .logout {
    position: absolute;
    top: 3px;
    right: -3px;
    background-color: var(--green);
    color: var(--white);
    border-radius: 50%;
    font-size: 1.3rem;
    height: 2rem;
    width: 2rem;
    justify-content: center;
  }
  /*  */
  @media only screen and (max-width: 1068px) {
    .nav-list {
      position: fixed;
      top: 12%;
      left: -35rem;
      flex-direction: column;
      align-items: flex-start;
      box-shadow: 5px 5px 10px rgba(0, 0, 0, 0.2);
      background-color: white;
      height: 100%;
      width: 0%;
      max-width: 35rem;
      z-index: 100;
      transition: all 300ms ease-in-out;
    }
  
    .navigation:hover{
      background-color: transparent;
    }
  
    
  
    .nav-list.open {
      left: 0;
      width: 100%;
    }
  
    .nav-list .nav-item {
      margin: 0 0 1rem 0;
      width: 100%;
    }
  
    .nav-list .nav-link {
      font-size: 1.5rem;
      color: var(--black);
      font-family: Inter;
      font-weight: 500;
      line-height: 20px;
      letter-spacing: 0.07em;
    }
  
    .nav-center .hamburger {
      display: block;
      color: var(--black);
      font-size: 3rem;
    }
  
    .icons {
      display: none ;
    }
    .iconss {
        display: none !important ;
      }
  
    .nav-list .icons {
      display: flex;
    }
  
    .top-nav ul {
      display: none ;
    }
  
    .top-nav div {
      justify-content: center;
      height: 3rem;
    }
  }

  
.container {
    max-width: 114rem;
    margin: 0 auto;
    padding: 0 1rem;
  }

  .d-flex {
    display: flex;
    align-items: center;
  }

  html{
    font-size: 62%;
    scroll-behavior: smooth;
  }
  .nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: #C9B194;
    transition: width 0.3s ease;
}

.nav-link:hover::after {
    width: 100%;
}

.nav-link.active::after {
    width: 100%;
}


@keyframes bounce {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-3px); }
}

.animate-bounce {
  animation: bounce 0.5s ease-in-out;
}