@extends('layouts.dashboard')

@section('title', 'Notifications')

@section('content')
    <div class="mb-6">
        <h1 class="text-2xl font-bold text-gray-900">Centre de notifications</h1>
        <p class="text-gray-600 mt-2"><PERSON><PERSON><PERSON> les alertes de stock et les demandes de précommandes</p>
    </div>

    <!-- Statistiques rapides -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div class="dashboard-card">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-red-100">
                    <i class="bx bx-package text-2xl text-red-600"></i>
                </div>
                <div class="ml-4">
                    <h3 class="text-lg font-semibold text-gray-900">{{ $outOfStockProducts->count() }}</h3>
                    <p class="text-sm text-gray-600">Produits en rupture</p>
                </div>
            </div>
        </div>

        <div class="dashboard-card">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-yellow-100">
                    <i class="bx bx-bell text-2xl text-yellow-600"></i>
                </div>
                <div class="ml-4">
                    <h3 class="text-lg font-semibold text-gray-900">{{ $preorderNotifications->count() }}</h3>
                    <p class="text-sm text-gray-600">Demandes de précommande</p>
                </div>
            </div>
        </div>

        <div class="dashboard-card">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-blue-100">
                    <i class="bx bx-info-circle text-2xl text-blue-600"></i>
                </div>
                <div class="ml-4">
                    <h3 class="text-lg font-semibold text-gray-900">{{ $outOfStockProducts->count() + $preorderNotifications->count() }}</h3>
                    <p class="text-sm text-gray-600">Total notifications</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Produits en rupture de stock -->
    <div class="dashboard-card mb-8">
        <div class="flex items-center justify-between pb-4 border-b">
            <h2 class="text-lg font-medium flex items-center">
                <i class="bx bx-package text-red-600 mr-2"></i>
                Produits en rupture de stock
            </h2>
            <span class="badge-danger">{{ $outOfStockProducts->count() }} produits</span>
        </div>

        @if($outOfStockProducts->count() > 0)
            <div class="overflow-x-auto mt-4">
                <table class="w-full text-sm text-left">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50">
                        <tr>
                            <th class="px-4 py-3">Produit</th>
                            <th class="px-4 py-3">Catégorie</th>
                            <th class="px-4 py-3">Prix</th>
                            <th class="px-4 py-3">Statut</th>
                            <th class="px-4 py-3">Demandes</th>
                            <th class="px-4 py-3">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($outOfStockProducts as $product)
                            <tr class="border-b hover:bg-gray-50">
                                <td class="px-4 py-3">
                                    <div class="flex items-center">
                                        <img src="{{ asset('storage/' . $product->image) }}" alt="{{ $product->name }}" class="w-12 h-12 object-cover rounded mr-3">
                                        <div>
                                            <div class="font-medium">{{ $product->name }}</div>
                                            <div class="text-gray-500 text-xs">{{ $product->color }}</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-4 py-3">{{ $product->category }}</td>
                                <td class="px-4 py-3">{{ number_format($product->price, 2) }}€</td>
                                <td class="px-4 py-3">
                                    @if(!$product->is_active)
                                        <span class="badge-danger">Inactif</span>
                                    @elseif(!$product->stock)
                                        <span class="badge-danger">Épuisé</span>
                                    @endif
                                </td>
                                <td class="px-4 py-3">
                                    <span class="text-sm font-medium">{{ $product->notifications->count() }}</span>
                                    <span class="text-xs text-gray-500">demandes</span>
                                </td>
                                <td class="px-4 py-3">
                                    <div class="flex items-center space-x-2">
                                        <a href="{{ route('dashboard.edit', $product->id) }}" class="text-blue-600 hover:text-blue-900" title="Modifier">
                                            <i class="bx bx-edit text-lg"></i>
                                        </a>
                                        @if(!$product->stock)
                                            <form action="{{ route('dashboard.toggle-stock', $product->id) }}" method="POST" class="inline">
                                                @csrf
                                                @method('PATCH')
                                                <button type="submit" class="text-green-600 hover:text-green-900" title="Remettre en stock">
                                                    <i class="bx bx-check-circle text-lg"></i>
                                                </button>
                                            </form>
                                        @endif
                                        @if(!$product->is_active)
                                            <form action="{{ route('dashboard.toggle-active', $product->id) }}" method="POST" class="inline">
                                                @csrf
                                                @method('PATCH')
                                                <button type="submit" class="text-green-600 hover:text-green-900" title="Activer">
                                                    <i class="bx bx-toggle-right text-lg"></i>
                                                </button>
                                            </form>
                                        @endif
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        @else
            <div class="text-center py-8">
                <i class="bx bx-check-circle text-4xl text-green-500 mb-4"></i>
                <p class="text-gray-500">Aucun produit en rupture de stock</p>
            </div>
        @endif
    </div>

    <!-- Demandes de précommandes -->
    <div class="dashboard-card">
        <div class="flex items-center justify-between pb-4 border-b">
            <h2 class="text-lg font-medium flex items-center">
                <i class="bx bx-bell text-yellow-600 mr-2"></i>
                Demandes de précommandes
            </h2>
            <span class="badge-danger">{{ $preorderNotifications->count() }} demandes</span>
        </div>

        @if($preorderNotifications->count() > 0)
            <div class="space-y-4 mt-4">
                @foreach($preorderNotifications as $notification)
                    <div class="border rounded-lg p-4 hover:bg-gray-50">
                        <div class="flex items-start justify-between">
                            <div class="flex items-start space-x-4">
                                @if($notification->product && $notification->product->image)
                                    <img src="{{ asset('storage/' . $notification->product->image) }}" alt="{{ $notification->product->name }}" class="w-16 h-16 object-cover rounded">
                                @else
                                    <div class="w-16 h-16 bg-gray-200 rounded flex items-center justify-center">
                                        <i class="bx bx-image text-gray-400"></i>
                                    </div>
                                @endif
                                <div class="flex-1">
                                    <h3 class="font-medium">{{ $notification->product->name ?? 'Produit supprimé' }}</h3>
                                    <p class="text-sm text-gray-600 mt-1">{{ $notification->email }}</p>
                                    <p class="text-xs text-gray-500 mt-2">
                                        Demandé le {{ $notification->created_at->format('d/m/Y à H:i') }}
                                    </p>
                                </div>
                            </div>
                            <div class="flex items-center space-x-2">
                                @if($notification->product)
                                    <a href="{{ route('dashboard.edit', $notification->product->id) }}" class="btn-secondary text-xs">
                                        Gérer le produit
                                    </a>
                                @endif
                                <form action="{{ route('dashboard.notifications.delete') }}" method="POST" class="inline">
                                    @csrf
                                    @method('DELETE')
                                    <input type="hidden" name="notification_id" value="{{ $notification->id }}">
                                    <button type="submit" class="text-red-600 hover:text-red-900" title="Supprimer la notification">
                                        <i class="bx bx-trash text-lg"></i>
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        @else
            <div class="text-center py-8">
                <i class="bx bx-bell-off text-4xl text-gray-400 mb-4"></i>
                <p class="text-gray-500">Aucune demande de précommande</p>
            </div>
        @endif
    </div>
@endsection

@push('scripts')
<script>
    // Auto-refresh notifications every 30 seconds
    setInterval(function() {
        // Only refresh if we're on the notifications page
        if (window.location.pathname.includes('/notifications')) {
            // You could implement AJAX refresh here if needed
        }
    }, 30000);
</script>
@endpush
