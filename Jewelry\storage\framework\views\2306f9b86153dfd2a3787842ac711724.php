<!DOCTYPE html>
<html lang="fr">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <!-- Box icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/boxicons@latest/css/boxicons.min.css" />
    <!-- Custom StyleSheet -->
    <?php echo app('Illuminate\Foundation\Vite')('resources/css/app.css'); ?>
    <link rel="stylesheet" href="<?php echo e(asset('css/index.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('css/nav.css')); ?>" />
    <title>Connexion - MAJĀA</title>
    <style>
      @keyframes border-grow {
        from { width: 0; }
        to { width: 25%; }
      }
      
      @keyframes input-appear {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
      }

      @keyframes error-shake {
        0%, 100% { transform: translateX(0); }
        25% { transform: translateX(8px); }
        75% { transform: translateX(-8px); }
      }
      .animate-border-grow {
        animation: border-grow 0.8s ease-out forwards;
      }

      .animate-input-appear {
        animation: input-appear 0.6s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
      }

      .animate-error-shake {
        animation: error-shake 0.4s ease-in-out;
      }

      @keyframes border-grow {
        from { width: 0; opacity: 0; }
        to { width: 25%; opacity: 1; }
      }

      @keyframes input-appear {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
      }

      @keyframes error-shake {
        0%, 100% { transform: translateX(0); }
        25% { transform: translateX(8px); }
        75% { transform: translateX(-8px); }
      }

      .animate-fade-in {
        animation: fade-in 0.6s ease-out forwards;
      }

      @keyframes fade-in {
        from { opacity: 0; }
        to { opacity: 1; }
      }
    </style>
  </head>
  <body class="bg-gray-50">
    <!-- En-tête -->
    <?php if (isset($component)) { $__componentOriginal3d4e3f5369e04c2cf115b9f764b9480e = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal3d4e3f5369e04c2cf115b9f764b9480e = $attributes; } ?>
<?php $component = App\View\Components\Nav::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('nav'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\Nav::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?> <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal3d4e3f5369e04c2cf115b9f764b9480e)): ?>
<?php $attributes = $__attributesOriginal3d4e3f5369e04c2cf115b9f764b9480e; ?>
<?php unset($__attributesOriginal3d4e3f5369e04c2cf115b9f764b9480e); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal3d4e3f5369e04c2cf115b9f764b9480e)): ?>
<?php $component = $__componentOriginal3d4e3f5369e04c2cf115b9f764b9480e; ?>
<?php unset($__componentOriginal3d4e3f5369e04c2cf115b9f764b9480e); ?>
<?php endif; ?>

    <!-- Connexion -->
    <div class="container">
      <div class="login-form animate-fade-in-up">
        <form action="<?php echo e(route('loginAction')); ?>" method="POST" class="space-y-6">
          <?php echo csrf_field(); ?>
          <h1 class="border-b-4 border-[#C9B194] w-1/4 mb-6 animate-border-grow [animation-delay:0.3s]">Connexion</h1>
          <p class="opacity-0 animate-fade-in [animation-delay:0.5s]">
            Vous avez déjà un compte? Connectez-vous ou
            <a href="<?php echo e(route('signup')); ?>" class="text-[#C9B194] hover:text-[#B89E80] transition-colors font-bold">
              Inscrivez-vous
            </a>
          </p>
      
          <div class="mb-4 opacity-0 animate-input-appear [animation-delay:0.7s]">
              <label for="email" class="block text-gray-700 mb-2">Email</label>
              <input type="text" placeholder="Entrez votre email" name="email" 
                     class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-[#C9B194] focus:ring-2 focus:ring-[#C9B194]/50 transition-all duration-300"
                     value="<?php echo e(old('email')); ?>" required />
              <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                <p class="text-red-500 text-lg mt-2 animate-error-shake"><?php echo e($message); ?></p>
              <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
          </div>
      
          <div class="mb-4 opacity-0 animate-input-appear [animation-delay:0.9s]">
              <label for="password" class="block text-gray-700 mb-2">Mot de passe</label>
              <input type="password" placeholder="Entrez votre mot de passe" name="password" 
                     class="w-full border-gray-300 rounded-md shadow-sm focus:border-[#C9B194] focus:ring-2 focus:ring-[#C9B194]/50 transition-all duration-300"
                     required />
              <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                  <p class="text-red-500 text-lg mt-2 animate-error-shake"><?php echo e($message); ?></p>
              <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
          </div>
      
          <div class="buttons opacity-0 animate-fade-in [animation-delay:1.1s]">
              <button type="submit" 
                      class="signupbtn bg-[#C9B194] hover:bg-[#B89E80] text-white px-8 py-3 rounded-full 
                             transition-all duration-300 hover:scale-105 hover:shadow-lg 
                             focus:outline-none focus:ring-2 focus:ring-[#C9B194] focus:ring-offset-2">
                Se connecter
              </button>
          </div>
        </form>
      </div>
    </div>

    <!-- Pied de page -->
    <?php if (isset($component)) { $__componentOriginal99051027c5120c83a2f9a5ae7c4c3cfa = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal99051027c5120c83a2f9a5ae7c4c3cfa = $attributes; } ?>
<?php $component = App\View\Components\Footer::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('footer'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\Footer::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?> <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal99051027c5120c83a2f9a5ae7c4c3cfa)): ?>
<?php $attributes = $__attributesOriginal99051027c5120c83a2f9a5ae7c4c3cfa; ?>
<?php unset($__attributesOriginal99051027c5120c83a2f9a5ae7c4c3cfa); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal99051027c5120c83a2f9a5ae7c4c3cfa)): ?>
<?php $component = $__componentOriginal99051027c5120c83a2f9a5ae7c4c3cfa; ?>
<?php unset($__componentOriginal99051027c5120c83a2f9a5ae7c4c3cfa); ?>
<?php endif; ?>

    <!-- Script personnalisé -->
    <script src="<?php echo e(asset('js/index.js')); ?>"></script>
  </body>
</html><?php /**PATH C:\Users\<USER>\Desktop\p\git_proj\Maja\Jewelry\resources\views/login.blade.php ENDPATH**/ ?>