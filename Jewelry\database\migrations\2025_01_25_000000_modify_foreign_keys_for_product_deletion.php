<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // First, modify the orders table
        Schema::table('orders', function (Blueprint $table) {
            // Drop the existing foreign key constraint
            $table->dropForeign(['product_id']);
            
            // Make the product_id column nullable
            $table->foreignId('product_id')->nullable()->change();
            
            // Add the new foreign key with onDelete('set null')
            $table->foreign('product_id')
                  ->references('id')
                  ->on('products')
                  ->onDelete('set null');
        });

        // Next, modify the product_notifications table
        Schema::table('product_notifications', function (Blueprint $table) {
            // Drop the existing foreign key constraint
            $table->dropForeign(['product_id']);
            
            // Make the product_id column nullable
            $table->foreignId('product_id')->nullable()->change();
            
            // Add the new foreign key with onDelete('set null')
            $table->foreign('product_id')
                  ->references('id')
                  ->on('products')
                  ->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Restore the original constraints for orders table
        Schema::table('orders', function (Blueprint $table) {
            // Drop the modified foreign key
            $table->dropForeign(['product_id']);
            
            // Make the product_id column non-nullable again
            $table->foreignId('product_id')->nullable(false)->change();
            
            // Add back the original foreign key without onDelete
            $table->foreign('product_id')
                  ->references('id')
                  ->on('products');
        });

        // Restore the original constraints for product_notifications table
        Schema::table('product_notifications', function (Blueprint $table) {
            // Drop the modified foreign key
            $table->dropForeign(['product_id']);
            
            // Make the product_id column non-nullable again
            $table->foreignId('product_id')->nullable(false)->change();
            
            // Add back the original foreign key without onDelete
            $table->foreign('product_id')
                  ->references('id')
                  ->on('products');
        });
    }
};
