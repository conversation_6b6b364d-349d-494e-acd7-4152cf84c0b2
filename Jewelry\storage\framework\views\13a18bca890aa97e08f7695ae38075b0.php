<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Dashboard - MAJĀA</title>
    
    
    <?php echo app('Illuminate\Foundation\Vite')('resources/css/app.css'); ?>
</head>
<body class="bg-[#F5F5F5] mx-auto flex  justify-center flex-col items-center min-h-screen">
    
    <h1 class="text-3xl font-bold mb-8 text-[#C9B194]">hello admin</h1>
    <form action="<?php echo e(route('logout')); ?>" method="POST">
        <?php echo csrf_field(); ?>
        <button type="submit" class="bg-[#C9B194] hover:bg-[#B89E80] text-white font-bold py-2 px-4 rounded-lg transition-all duration-300">
            Déconnexion
        </button>
    </form>

    <div class="container mx-auto px-4 py-8 justify-center flex flex-col items-center">
        <h1 class="text-3xl font-bold mb-8 text-[#C9B194]">Ajouter un nouveau produit</h1>
        
        <form action="<?php echo e(route('store')); ?>" method="POST" enctype="multipart/form-data" class="max-w-2xl bg-white rounded-lg shadow-md p-6">
            <?php echo csrf_field(); ?>
        
            <!-- Message de succès -->
            <?php if(session('success')): ?>
                <div class="mb-6 p-4 bg-green-100 text-green-700 rounded-lg border border-green-200">
                    <?php echo e(session('success')); ?>

                </div>
            <?php endif; ?>
        
            <div class="space-y-6">
                <!-- Champ Nom -->
                <div>
                    <label class="block text-gray-700 mb-2 font-medium">Nom du produit</label>
                    <input type="text" name="name" value="<?php echo e(old('name')); ?>" required 
                        class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:border-[#C9B194]">
                    <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <div class="text-red-500 text-sm mt-1"><?php echo e($message); ?></div>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>
        
                <!-- Champ Catégorie -->
                <div>
                    <label class="block text-gray-700 mb-2 font-medium">Catégorie</label>
                    <select name="category" required 
                        class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:border-[#C9B194]">
                        <option value="">Sélectionner une catégorie</option>
                        <option value="ABAYA" <?php if(old('category') == 'ABAYA'): echo 'selected'; endif; ?>>ABAYA</option>
                        <option value="HIJAB" <?php if(old('category') == 'HIJAB'): echo 'selected'; endif; ?>>HIJAB</option>
                        <option value="ACCESSORY" <?php if(old('category') == 'ACCESSORY'): echo 'selected'; endif; ?>>ACCESSORY</option>
                        <option value="KIMONO" <?php if(old('category') == 'KIMONO'): echo 'selected'; endif; ?>>KIMONO</option>
                    </select>
                    <?php $__errorArgs = ['category'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <div class="text-red-500 text-sm mt-1"><?php echo e($message); ?></div>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>
        
                <!-- Image principale -->
                <div>
                    <label class="block text-gray-700 mb-2 font-medium">Image principale</label>
                    <input type="file" name="image" required 
                        class="w-full file:mr-4 file:py-2 file:px-4 file:rounded file:border-0 file:text-sm file:font-semibold
                        file:bg-[#C9B194]/20 file:text-[#C9B194] hover:file:bg-[#C9B194]/30">
                    <?php $__errorArgs = ['image'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <div class="text-red-500 text-sm mt-1"><?php echo e($message); ?></div>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>
        
                <!-- Galerie d'images -->
                <div>
                    <label class="block text-gray-700 mb-2 font-medium">Galerie d'images</label>
                    <input type="file" name="images[]" multiple 
                        class="w-full file:mr-4 file:py-2 file:px-4 file:rounded file:border-0 file:text-sm file:font-semibold
                        file:bg-[#C9B194]/20 file:text-[#C9B194] hover:file:bg-[#C9B194]/30">
                    <?php $__errorArgs = ['images.*'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <div class="text-red-500 text-sm mt-1"><?php echo e($message); ?></div>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>
        
                <!-- Description -->
                <div>
                    <label class="block text-gray-700 mb-2 font-medium">Description</label>
                    <textarea name="description" rows="4" required 
                        class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:border-[#C9B194]"><?php echo e(old('description')); ?></textarea>
                    <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <div class="text-red-500 text-sm mt-1"><?php echo e($message); ?></div>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>
        
                <!-- Prix -->
                <div>
                    <label class="block text-gray-700 mb-2 font-medium">Prix avant remise (€)</label>
                    <input type="number" step="0.01" name="price_before_discount" value="<?php echo e(old('price_before_discount')); ?>" required 
                        class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:border-[#C9B194]">
                    <?php $__errorArgs = ['price_before_discount'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <div class="text-red-500 text-sm mt-1"><?php echo e($message); ?></div>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <!-- Prix -->
                <div>
                    <label class="block text-gray-700 mb-2 font-medium">Prix apres remise (€)</label>
                    <input type="number" step="0.01" name="price" value="<?php echo e(old('price')); ?>" required 
                        class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:border-[#C9B194]">
                    <?php $__errorArgs = ['price'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <div class="text-red-500 text-sm mt-1"><?php echo e($message); ?></div>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>
        
                <!-- Taille -->
                
        
                <!-- Couleur -->
                <div>
                    <label class="block text-gray-700 mb-2 font-medium">Colour</label>
                    <select name="color" required 
                        class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:border-[#C9B194]">
                        <option value="">Sélectionner une couleur</option>
                        <option value="Biege" <?php if(old('color') == 'Biege'): echo 'selected'; endif; ?>>Biege</option>
                        <option value="Taupe" <?php if(old('color') == 'Taupe'): echo 'selected'; endif; ?>>Taupe</option>
                        <option value="Marron" <?php if(old('color') == 'Marron'): echo 'selected'; endif; ?>>Marron</option>
                    </select>
                    <?php $__errorArgs = ['color'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <div class="text-red-500 text-sm mt-1"><?php echo e($message); ?></div>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>
        
                <button type="submit" 
                    class="w-full bg-[#C9B194] hover:bg-[#B89E80] text-white font-bold py-3 px-4 rounded-lg transition-all duration-300">
                    Ajouter le produit
                </button>
            </div>
        </form>
    </div>
    
</body>
</html><?php /**PATH C:\Users\<USER>\Desktop\p\git_proj\Maja\Jewelry\resources\views/dashboard.blade.php ENDPATH**/ ?>