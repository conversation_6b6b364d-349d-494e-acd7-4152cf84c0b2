<?php $__env->startSection('title', 'Modifier un produit'); ?>

<?php $__env->startSection('content'); ?>
    <div class="mb-6 flex items-center justify-between">
        <h1 class="text-2xl font-bold text-gray-900">Modifier le produit</h1>
        <div class="flex space-x-2">
            <a href="<?php echo e(route('dashboard.show', $product->id)); ?>" class="btn-secondary flex items-center">
                <i class="bx bx-show mr-2"></i>
                Voir
            </a>
            <a href="<?php echo e(route('dashboard')); ?>" class="btn-secondary flex items-center">
                <i class="bx bx-arrow-back mr-2"></i>
                Retour
            </a>
        </div>
    </div>

    <div class="dashboard-card">
        <form action="<?php echo e(route('dashboard.update', $product->id)); ?>" method="POST" enctype="multipart/form-data" id="product-form">
            <?php echo csrf_field(); ?>
            <?php echo method_field('PUT'); ?>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Informations de base -->
                <div class="space-y-6">
                    <h2 class="text-lg font-medium border-b pb-2">Informations de base</h2>
                    
                    <!-- Nom du produit -->
                    <div>
                        <label for="name" class="form-label">Nom du produit</label>
                        <input type="text" id="name" name="name" value="<?php echo e(old('name', $product->name)); ?>" required class="form-input">
                        <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="form-error"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    
                    <!-- Catégorie -->
                    <div>
                        <label for="category" class="form-label">Catégorie</label>
                        <select id="category" name="category" required class="form-input">
                            <option value="">Sélectionner une catégorie</option>
                            <option value="ABAYA" <?php if(old('category', $product->category) == 'ABAYA'): echo 'selected'; endif; ?>>ABAYA</option>
                            <option value="HIJAB" <?php if(old('category', $product->category) == 'HIJAB'): echo 'selected'; endif; ?>>HIJAB</option>
                            <option value="ACCESSORY" <?php if(old('category', $product->category) == 'ACCESSORY'): echo 'selected'; endif; ?>>ACCESSORY</option>
                            <option value="KIMONO" <?php if(old('category', $product->category) == 'KIMONO'): echo 'selected'; endif; ?>>KIMONO</option>
                        </select>
                        <?php $__errorArgs = ['category'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="form-error"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    
                    <!-- Couleur -->
                    <div>
                        <label for="color" class="form-label">Couleur</label>
                        <input type="text" id="color" name="color" value="<?php echo e(old('color', $product->color)); ?>" required class="form-input">
                        <?php $__errorArgs = ['color'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="form-error"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    
                    <!-- Prix -->
                    <div>
                        <label for="price" class="form-label">Prix (€)</label>
                        <input type="number" id="price" name="price" value="<?php echo e(old('price', $product->price)); ?>" step="0.01" required class="form-input">
                        <?php $__errorArgs = ['price'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="form-error"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    
                    <!-- Prix avant réduction -->
                    <div>
                        <label for="price_before_discount" class="form-label">Prix avant réduction (€) <span class="text-gray-500 text-sm">(optionnel)</span></label>
                        <input type="number" id="price_before_discount" name="price_before_discount" value="<?php echo e(old('price_before_discount', $product->price_before_discount)); ?>" step="0.01" class="form-input">
                        <?php $__errorArgs = ['price_before_discount'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="form-error"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    
                    <!-- Statut et stock -->
                    <div class="flex space-x-6">
                        <div class="flex items-center">
                            <input type="checkbox" id="is_active" name="is_active" value="1" <?php if(old('is_active', $product->is_active)): echo 'checked'; endif; ?> class="w-4 h-4 text-[#C9B194] rounded focus:ring-[#C9B194]">
                            <label for="is_active" class="ml-2 text-sm font-medium">Produit actif</label>
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" id="stock" name="stock" value="1" <?php if(old('stock', $product->stock)): echo 'checked'; endif; ?> class="w-4 h-4 text-[#C9B194] rounded focus:ring-[#C9B194]">
                            <label for="stock" class="ml-2 text-sm font-medium">En stock</label>
                        </div>
                    </div>
                </div>
                
                <!-- Médias et description -->
                <div class="space-y-6">
                    <h2 class="text-lg font-medium border-b pb-2">Médias et description</h2>
                    
                    <!-- Image principale -->
                    <div>
                        <label for="image" class="form-label">Image principale</label>
                        <div class="mt-1 flex items-center">
                            <div id="image-preview" class="w-32 h-32 border-2 border-gray-300 rounded-lg flex items-center justify-center mb-2 overflow-hidden">
                                <?php if($product->image): ?>
                                    <img src="<?php echo e(asset('storage/' . $product->image)); ?>" class="w-full h-full object-cover">
                                <?php else: ?>
                                    <i class="bx bx-image-add text-4xl text-gray-400"></i>
                                <?php endif; ?>
                            </div>
                        </div>
                        <input type="file" id="image" name="image" accept="image/*" class="mt-2 w-full file:mr-4 file:py-2 file:px-4 file:rounded file:border-0 file:text-sm file:font-semibold file:bg-[#C9B194]/20 file:text-[#C9B194] hover:file:bg-[#C9B194]/30">
                        <p class="text-sm text-gray-500 mt-1">Laissez vide pour conserver l'image actuelle</p>
                        <?php $__errorArgs = ['image'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="form-error"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    
                    <!-- Galerie d'images -->
                    <div>
                        <label for="images" class="form-label">Galerie d'images</label>
                        <div class="mt-1 flex flex-wrap gap-2" id="gallery-preview">
                            <?php if($product->images): ?>
                                <?php $__currentLoopData = $product->images; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $image): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="w-20 h-20 border rounded-lg overflow-hidden">
                                        <img src="<?php echo e(asset('storage/' . $image)); ?>" class="w-full h-full object-cover">
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <?php endif; ?>
                        </div>
                        <input type="file" id="images" name="images[]" accept="image/*" multiple class="mt-2 w-full file:mr-4 file:py-2 file:px-4 file:rounded file:border-0 file:text-sm file:font-semibold file:bg-[#C9B194]/20 file:text-[#C9B194] hover:file:bg-[#C9B194]/30">
                        <p class="text-sm text-gray-500 mt-1">Laissez vide pour conserver les images actuelles</p>
                        <?php $__errorArgs = ['images.*'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="form-error"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    
                    <!-- Description -->
                    <div>
                        <label for="description" class="form-label">Description</label>
                        <textarea id="description" name="description" rows="5" required class="form-input"><?php echo e(old('description', $product->description)); ?></textarea>
                        <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="form-error"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                </div>
            </div>
            
            <div class="mt-8 border-t pt-6 flex justify-between">
                <form action="<?php echo e(route('dashboard.destroy', $product->id)); ?>" method="POST" class="inline delete-form">
                    <?php echo csrf_field(); ?>
                    <?php echo method_field('DELETE'); ?>
                    <button type="submit" class="btn-danger flex items-center">
                        <i class="bx bx-trash mr-2"></i>
                        Supprimer
                    </button>
                </form>
                
                <button type="submit" class="btn-primary">
                    <i class="bx bx-save mr-2"></i>
                    Enregistrer les modifications
                </button>
            </div>
        </form>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
    // Image preview
    const imageInput = document.getElementById('image');
    const imagePreview = document.getElementById('image-preview');
    
    imageInput.addEventListener('change', function() {
        if (this.files && this.files[0]) {
            const reader = new FileReader();
            reader.onload = function(e) {
                imagePreview.innerHTML = `<img src="${e.target.result}" class="w-full h-full object-cover">`;
            }
            reader.readAsDataURL(this.files[0]);
        }
    });
    
    // Gallery preview for new uploads
    const galleryInput = document.getElementById('images');
    const galleryPreview = document.getElementById('gallery-preview');
    const currentGallery = galleryPreview.innerHTML;
    
    galleryInput.addEventListener('change', function() {
        galleryPreview.innerHTML = '';
        
        if (this.files) {
            Array.from(this.files).forEach(file => {
                const reader = new FileReader();
                reader.onload = function(e) {
                    galleryPreview.innerHTML += `
                        <div class="w-20 h-20 border rounded-lg overflow-hidden">
                            <img src="${e.target.result}" class="w-full h-full object-cover">
                        </div>
                    `;
                }
                reader.readAsDataURL(file);
            });
        } else {
            galleryPreview.innerHTML = currentGallery;
        }
    });
    
    // Delete confirmation
    document.querySelector('.delete-form')?.addEventListener('submit', function(e) {
        e.preventDefault();
        if (confirm('Êtes-vous sûr de vouloir supprimer ce produit ? Cette action est irréversible.')) {
            this.submit();
        }
    });
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.dashboard', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\p\git_proj\Maja\Jewelry\resources\views/dashboard/edit.blade.php ENDPATH**/ ?>