<?php $__env->startSection('title', 'Tableau de bord'); ?>

<?php $__env->startSection('content'); ?>
    <div class="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <h1 class="text-2xl font-bold text-gray-900">Tableau de bord</h1>
        <div class="mt-4 sm:mt-0">
            <a href="<?php echo e(route('dashboard.create')); ?>" class="btn-primary flex items-center">
                <i class="bx bx-plus mr-2"></i>
                Ajouter un produit
            </a>
        </div>
    </div>

    <!-- Statistiques rapides -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="dashboard-card">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-blue-100">
                    <i class="bx bx-package text-2xl text-blue-600"></i>
                </div>
                <div class="ml-4">
                    <h3 class="text-lg font-semibold text-gray-900"><?php echo e($products->total()); ?></h3>
                    <p class="text-sm text-gray-600">Total produits</p>
                </div>
            </div>
        </div>

        <div class="dashboard-card">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-green-100">
                    <i class="bx bx-check-circle text-2xl text-green-600"></i>
                </div>
                <div class="ml-4">
                    <h3 class="text-lg font-semibold text-gray-900"><?php echo e($products->total() - $outOfStockProducts); ?></h3>
                    <p class="text-sm text-gray-600">Produits actifs</p>
                </div>
            </div>
        </div>

        <div class="dashboard-card">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-red-100">
                    <i class="bx bx-x-circle text-2xl text-red-600"></i>
                </div>
                <div class="ml-4">
                    <h3 class="text-lg font-semibold text-gray-900"><?php echo e($outOfStockProducts); ?></h3>
                    <p class="text-sm text-gray-600">Ruptures de stock</p>
                </div>
            </div>
        </div>

        <div class="dashboard-card">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-yellow-100">
                    <i class="bx bx-bell text-2xl text-yellow-600"></i>
                </div>
                <div class="ml-4">
                    <h3 class="text-lg font-semibold text-gray-900"><?php echo e($preorderNotifications); ?></h3>
                    <p class="text-sm text-gray-600">Précommandes</p>
                </div>
            </div>
        </div>
    </div>

    <?php if($totalNotifications > 0): ?>
        <div class="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <div class="flex items-center">
                <i class="bx bx-info-circle text-yellow-600 text-xl mr-3"></i>
                <div>
                    <h3 class="font-medium text-yellow-800">Attention requise</h3>
                    <p class="text-sm text-yellow-700 mt-1">
                        Vous avez <?php echo e($totalNotifications); ?> notification(s) en attente. 
                        <a href="<?php echo e(route('dashboard.notifications')); ?>" class="underline hover:no-underline">Voir les notifications</a>
                    </p>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <div class="dashboard-card overflow-hidden">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between pb-4 border-b">
            <h2 class="text-lg font-medium">Liste des produits</h2>
            <div class="mt-3 sm:mt-0 flex items-center space-x-2">
                <span class="text-sm text-gray-500"><?php echo e($products->total()); ?> produits</span>
            </div>
        </div>

        <div class="overflow-x-auto">
            <table class="w-full text-sm text-left">
                <thead class="text-xs text-gray-700 uppercase bg-gray-50">
                    <tr>
                        <th class="px-4 py-3">Image</th>
                        <th class="px-4 py-3">Nom</th>
                        <th class="px-4 py-3">Catégorie</th>
                        <th class="px-4 py-3">Prix</th>
                        <th class="px-4 py-3">Statut</th>
                        <th class="px-4 py-3">Stock</th>
                        <th class="px-4 py-3">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php $__empty_1 = true; $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr class="border-b hover:bg-gray-50 <?php echo e((!$product->is_active || !$product->stock) ? 'bg-red-50' : ''); ?>">
                            <td class="px-4 py-3">
                                <img src="<?php echo e(asset('storage/' . $product->image)); ?>" alt="<?php echo e($product->name); ?>" class="w-16 h-16 object-cover rounded">
                            </td>
                            <td class="px-4 py-3 font-medium">
                                <?php echo e($product->name); ?>

                                <?php if(!$product->is_active || !$product->stock): ?>
                                    <i class="bx bx-error-circle text-red-500 ml-1" title="Rupture de stock"></i>
                                <?php endif; ?>
                            </td>
                            <td class="px-4 py-3"><?php echo e($product->category); ?></td>
                            <td class="px-4 py-3"><?php echo e(number_format($product->price, 2)); ?>€</td>
                            <td class="px-4 py-3">
                                <form action="<?php echo e(route('dashboard.toggle-active', $product->id)); ?>" method="POST" class="inline">
                                    <?php echo csrf_field(); ?>
                                    <?php echo method_field('PATCH'); ?>
                                    <button type="submit" class="<?php echo e($product->is_active ? 'badge-success' : 'badge-danger'); ?>">
                                        <?php echo e($product->is_active ? 'Actif' : 'Inactif'); ?>

                                    </button>
                                </form>
                            </td>
                            <td class="px-4 py-3">
                                <form action="<?php echo e(route('dashboard.toggle-stock', $product->id)); ?>" method="POST" class="inline">
                                    <?php echo csrf_field(); ?>
                                    <?php echo method_field('PATCH'); ?>
                                    <button type="submit" class="<?php echo e($product->stock ? 'badge-success' : 'badge-danger'); ?>">
                                        <?php echo e($product->stock ? 'En stock' : 'Épuisé'); ?>

                                    </button>
                                </form>
                            </td>
                            <td class="px-4 py-3">
                                <div class="flex items-center space-x-2">
                                    <a href="<?php echo e(route('dashboard.show', $product->id)); ?>" class="text-blue-600 hover:text-blue-900" title="Voir">
                                        <i class="bx bx-show text-xl"></i>
                                    </a>
                                    <a href="<?php echo e(route('dashboard.edit', $product->id)); ?>" class="text-yellow-600 hover:text-yellow-900" title="Modifier">
                                        <i class="bx bx-edit text-xl"></i>
                                    </a>
                                    <form action="<?php echo e(route('dashboard.destroy', $product->id)); ?>" method="POST" class="inline delete-form">
                                        <?php echo csrf_field(); ?>
                                        <?php echo method_field('DELETE'); ?>
                                        <button type="submit" class="text-red-600 hover:text-red-900" title="Supprimer">
                                            <i class="bx bx-trash text-xl"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="7" class="px-4 py-6 text-center text-gray-500">
                                Aucun produit trouvé. <a href="<?php echo e(route('dashboard.create')); ?>" class="text-[#C9B194] hover:underline">Ajouter un produit</a>
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

        <div class="px-4 py-3">
            <?php echo e($products->links()); ?>

        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
    // Confirmation dialog for delete
    document.querySelectorAll('.delete-form').forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            if (confirm('Êtes-vous sûr de vouloir supprimer ce produit ? Cette action est irréversible.')) {
                this.submit();
            }
        });
    });
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.dashboard', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\p\git_proj\Maja\Jewelry\resources\views/dashboard/index.blade.php ENDPATH**/ ?>