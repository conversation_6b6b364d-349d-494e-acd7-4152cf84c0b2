<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class Product extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'category',
        'image',
        'images',
        'description',
        'price',
        'price_before_discount',
        'is_active',
        'stock',
        'slug',
        'color',
    ];

    protected $casts = [
        'images' => 'array',
    ];
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($product) {
            $originalSlug = $slug = Str::slug($product->name);
            $counter = 1;

            while (static::where('slug', $slug)->exists()) {
                $slug = $originalSlug . '-' . $counter;
                $counter++;
            }

            $product->slug = $slug;
        });
    }

    // Relationships
    public function notifications()
    {
        return $this->hasMany(ProductNotification::class);
    }

    public function orders()
    {
        return $this->hasMany(Order::class);
    }

    // Scopes
    public function scopeOutOfStock($query)
    {
        return $query->where('is_active', false)->orWhere('stock', false);
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeInStock($query)
    {
        return $query->where('stock', true);
    }

    // Accessors
    public function getIsOutOfStockAttribute()
    {
        return !$this->is_active || !$this->stock;
    }

    public function getNotificationCountAttribute()
    {
        return $this->notifications()->count();
    }
}
