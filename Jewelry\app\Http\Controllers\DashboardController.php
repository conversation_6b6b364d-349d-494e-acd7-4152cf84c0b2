<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Product;
use App\Models\ProductNotification;

use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Artisan;

class DashboardController extends Controller
{
    public function index()
    {
        $products = Product::latest()->paginate(10);

        // Get notifications data
        $outOfStockProducts = Product::outOfStock()->count();
        $preorderNotifications = ProductNotification::whereHas('product', function($query) {
            $query->outOfStock();
        })->count();
        $totalNotifications = $outOfStockProducts + $preorderNotifications;

        return view('dashboard.index', compact('products', 'outOfStockProducts', 'preorderNotifications', 'totalNotifications'));
    }

    public function create()
    {
        return view('dashboard.create');
    }

    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'category' => 'required|string|max:255',
            'image' => 'required|image',
            'images.*' => 'required|image',
            'description' => 'required|string',
            'price' => 'required|numeric',
            'price_before_discount' => 'nullable|numeric',
            'color' => 'required|string',
            'is_active' => 'boolean',
            'stock' => 'boolean',
        ]);

        $image = $request->file('image');
        $imagePath = $image->store('products', 'public');

        $images = [];
        foreach ($request->file('images') as $img) {
            $path = $img->store('products', 'public');
            $images[] = $path;
        }

        Product::create([
            'name' => $request->name,
            'category' => $request->category,
            'image' => $imagePath,
            'images' => $images,
            'description' => $request->description,
            'color' => $request->color,
            'price' => $request->price,
            'price_before_discount' => $request->price_before_discount,
            'is_active' => $request->has('is_active'),
            'stock' => $request->has('stock'),
        ]);

        return redirect()->route('dashboard')->with('success', 'Produit ajouté avec succès.');
    }

    public function edit($id)
    {
        $product = Product::findOrFail($id);
        return view('dashboard.edit', compact('product'));
    }

    public function update(Request $request, $id)
    {
        $product = Product::findOrFail($id);

        $request->validate([
            'name' => 'required|string|max:255',
            'category' => 'required|string|max:255',
            'image' => 'nullable|image',
            'images.*' => 'nullable|image',
            'description' => 'required|string',
            'price' => 'required|numeric',
            'price_before_discount' => 'nullable|numeric',
            'color' => 'required|string',
            'is_active' => 'boolean',
            'stock' => 'boolean',
        ]);

        $data = [
            'name' => $request->name,
            'category' => $request->category,
            'description' => $request->description,
            'color' => $request->color,
            'price' => $request->price,
            'price_before_discount' => $request->price_before_discount,
            'is_active' => $request->has('is_active'),
            'stock' => $request->has('stock'),
        ];

        // Handle main image update
        if ($request->hasFile('image')) {
            // Delete old image
            if ($product->image) {
                Storage::disk('public')->delete($product->image);
            }

            $image = $request->file('image');
            $data['image'] = $image->store('products', 'public');
        }

        // Handle gallery images update
        if ($request->hasFile('images')) {
            // Delete old images
            if (!empty($product->images)) {
                foreach ($product->images as $img) {
                    Storage::disk('public')->delete($img);
                }
            }

            $images = [];
            foreach ($request->file('images') as $img) {
                $path = $img->store('products', 'public');
                $images[] = $path;
            }
            $data['images'] = $images;
        }

        $product->update($data);

        return redirect()->route('dashboard')->with('success', 'Produit mis à jour avec succès.');
    }

    public function show($id)
    {
        $product = Product::findOrFail($id);
        return view('dashboard.show', compact('product'));
    }

    public function destroy($id)
    {
        try {
            $product = Product::findOrFail($id);

            // Delete images from storage
            if ($product->image) {
                Storage::disk('public')->delete($product->image);
            }

            if (!empty($product->images)) {
                foreach ($product->images as $img) {
                    Storage::disk('public')->delete($img);
                }
            }

            $product->delete();

            return redirect()->route('dashboard')->with('success', 'Produit supprimé avec succès.');
        } catch (\Exception $e) {
            // Check if it's a foreign key constraint error
            if (strpos($e->getMessage(), 'foreign key constraint fails') !== false) {
                // Run the migration to modify the foreign key constraints
                Artisan::call('migrate', ['--path' => 'database/migrations/2025_07_25_000000_modify_foreign_keys_for_product_deletion.php']);

                // Try deleting again
                $product = Product::findOrFail($id);

                // Delete images from storage
                if ($product->image) {
                    Storage::disk('public')->delete($product->image);
                }

                if (!empty($product->images)) {
                    foreach ($product->images as $img) {
                        Storage::disk('public')->delete($img);
                    }
                }

                $product->delete();

                return redirect()->route('dashboard')->with('success', 'Produit supprimé avec succès. Les références à ce produit dans les commandes ont été conservées.');
            }

            return redirect()->route('dashboard')->with('error', 'Erreur lors de la suppression du produit: ' . $e->getMessage());
        }
    }

    public function toggleActive($id)
    {
        $product = Product::findOrFail($id);
        $product->is_active = !$product->is_active;
        $product->save();

        return redirect()->back()->with('success', 'Statut du produit mis à jour.');
    }

    public function toggleStock($id)
    {
        $product = Product::findOrFail($id);
        $product->stock = !$product->stock;
        $product->save();

        return redirect()->back()->with('success', 'Stock du produit mis à jour.');
    }

    public function notifications()
    {
        $outOfStockProducts = Product::outOfStock()->with('notifications')->get();
        $preorderNotifications = ProductNotification::whereHas('product', function($query) {
            $query->outOfStock();
        })->with('product')->latest()->get();

        return view('dashboard.notifications', compact('outOfStockProducts', 'preorderNotifications'));
    }

    public function deleteNotification(Request $request)
    {
        $notification = ProductNotification::findOrFail($request->notification_id);
        $notification->delete();

        return redirect()->back()->with('success', 'Notification supprimée avec succès.');
    }
}
