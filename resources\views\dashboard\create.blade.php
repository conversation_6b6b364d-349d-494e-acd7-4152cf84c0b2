@extends('layouts.dashboard')

@section('title', 'Ajouter un produit')

@section('content')
    <div class="mb-6 flex items-center justify-between">
        <h1 class="text-2xl font-bold text-gray-900">Ajouter un nouveau produit</h1>
        <a href="{{ route('dashboard') }}" class="btn-secondary flex items-center">
            <i class="bx bx-arrow-back mr-2"></i>
            Retour
        </a>
    </div>

    <div class="dashboard-card">
        <form action="{{ route('dashboard.store') }}" method="POST" enctype="multipart/form-data" id="product-form">
            @csrf
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Informations de base -->
                <div class="space-y-6">
                    <h2 class="text-lg font-medium border-b pb-2">Informations de base</h2>
                    
                    <!-- Nom du produit -->
                    <div>
                        <label for="name" class="form-label">Nom du produit</label>
                        <input type="text" id="name" name="name" value="{{ old('name') }}" required class="form-input">
                        @error('name')
                            <div class="form-error">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <!-- Catégorie -->
                    <div>
                        <label for="category" class="form-label">Catégorie</label>
                        <select id="category" name="category" required class="form-input">
                            <option value="">Sélectionner une catégorie</option>
                            <option value="ABAYA" @selected(old('category') == 'ABAYA')>ABAYA</option>
                            <option value="HIJAB" @selected(old('category') == 'HIJAB')>HIJAB</option>
                            <option value="ACCESSORY" @selected(old('category') == 'ACCESSORY')>ACCESSORY</option>
                            <option value="KIMONO" @selected(old('category') == 'KIMONO')>KIMONO</option>
                        </select>
                        @error('category')
                            <div class="form-error">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <!-- Couleur -->
                    <div>
                        <label for="color" class="form-label">Couleur</label>
                        <input type="text" id="color" name="color" value="{{ old('color') }}" required class="form-input">
                        @error('color')
                            <div class="form-error">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <!-- Prix -->
                    <div>
                        <label for="price" class="form-label">Prix (€)</label>
                        <input type="number" id="price" name="price" value="{{ old('price') }}" step="0.01" required class="form-input">
                        @error('price')
                            <div class="form-error">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <!-- Prix avant réduction -->
                    <div>
                        <label for="price_before_discount" class="form-label">Prix avant réduction (€) <span class="text-gray-500 text-sm">(optionnel)</span></label>
                        <input type="number" id="price_before_discount" name="price_before_discount" value="{{ old('price_before_discount') }}" step="0.01" class="form-input">
                        @error('price_before_discount')
                            <div class="form-error">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <!-- Statut et stock -->
                    <div class="flex space-x-6">
                        <div class="flex items-center">
                            <input type="checkbox" id="is_active" name="is_active" value="1" @checked(old('is_active', true)) class="w-4 h-4 text-[#C9B194] rounded focus:ring-[#C9B194]">
                            <label for="is_active" class="ml-2 text-sm font-medium">Produit actif</label>
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" id="stock" name="stock" value="1" @checked(old('stock', true)) class="w-4 h-4 text-[#C9B194] rounded focus:ring-[#C9B194]">
                            <label for="stock" class="ml-2 text-sm font-medium">En stock</label>
                        </div>
                    </div>
                </div>
                
                <!-- Médias et description -->
                <div class="space-y-6">
                    <h2 class="text-lg font-medium border-b pb-2">Médias et description</h2>
                    
                    <!-- Image principale -->
                    <div>
                        <label for="image" class="form-label">Image principale</label>
                        <div class="mt-1 flex items-center">
                            <div id="image-preview" class="w-32 h-32 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center mb-2 overflow-hidden">
                                <i class="bx bx-image-add text-4xl text-gray-400"></i>
                            </div>
                        </div>
                        <input type="file" id="image" name="image" accept="image/*" required class="mt-2 w-full file:mr-4 file:py-2 file:px-4 file:rounded file:border-0 file:text-sm file:font-semibold file:bg-[#C9B194]/20 file:text-[#C9B194] hover:file:bg-[#C9B194]/30">
                        @error('image')
                            <div class="form-error">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <!-- Galerie d'images -->
                    <div>
                        <label for="images" class="form-label">Galerie d'images</label>
                        <div class="mt-1 flex flex-wrap gap-2" id="gallery-preview">
                            <!-- Preview images will be displayed here -->
                        </div>
                        <input type="file" id="images" name="images[]" accept="image/*" multiple required class="mt-2 w-full file:mr-4 file:py-2 file:px-4 file:rounded file:border-0 file:text-sm file:font-semibold file:bg-[#C9B194]/20 file:text-[#C9B194] hover:file:bg-[#C9B194]/30">
                        @error('images.*')
                            <div class="form-error">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <!-- Description -->
                    <div>
                        <label for="description" class="form-label">Description</label>
                        <textarea id="description" name="description" rows="5" required class="form-input">{{ old('description') }}</textarea>
                        @error('description')
                            <div class="form-error">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>
            
            <div class="mt-8 border-t pt-6 flex justify-end">
                <button type="submit" class="btn-primary">
                    <i class="bx bx-save mr-2"></i>
                    Ajouter le produit
                </button>
            </div>
        </form>
    </div>
@endsection

@push('scripts')
<script>
    // Image preview
    const imageInput = document.getElementById('image');
    const imagePreview = document.getElementById('image-preview');
    
    imageInput.addEventListener('change', function() {
        if (this.files && this.files[0]) {
            const reader = new FileReader();
            reader.onload = function(e) {
                imagePreview.innerHTML = `<img src="${e.target.result}" class="w-full h-full object-cover">`;
            }
            reader.readAsDataURL(this.files[0]);
        }
    });
    
    // Gallery preview
    const galleryInput = document.getElementById('images');
    const galleryPreview = document.getElementById('gallery-preview');
    
    galleryInput.addEventListener('change', function() {
        galleryPreview.innerHTML = '';
        
        if (this.files) {
            Array.from(this.files).forEach(file => {
                const reader = new FileReader();
                reader.onload = function(e) {
                    galleryPreview.innerHTML += `
                        <div class="w-20 h-20 border rounded-lg overflow-hidden">
                            <img src="${e.target.result}" class="w-full h-full object-cover">
                        </div>
                    `;
                }
                reader.readAsDataURL(file);
            });
        }
    });
    
    // Form validation
    document.getElementById('product-form').addEventListener('submit', function(e) {
        const requiredFields = this.querySelectorAll('[required]');
        let valid = true;
        
        requiredFields.forEach(field => {
            if (!field.value) {
                valid = false;
                field.classList.add('border-red-500');
            } else {
                field.classList.remove('border-red-500');
            }
        });
        
        if (!valid) {
            e.preventDefault();
            alert('Veuillez remplir tous les champs obligatoires.');
        }
    });
</script>
@endpush
