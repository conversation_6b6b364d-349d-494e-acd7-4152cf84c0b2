<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class AdminMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Vérifier si l'utilisateur est connecté
        if (!auth()->check()) {
            return redirect()->route('login')->with('error', 'Vous devez être connecté pour accéder au tableau de bord.');
        }

        // Vérifier si l'utilisateur est un administrateur
        // Pour cet exemple, nous considérons que tous les utilisateurs connectés sont des admins
        // Vous pouvez ajouter une colonne 'is_admin' ou 'role' à votre table users pour une vérification plus stricte
        $user = auth()->user();

        // Exemple de vérification par email (vous pouvez adapter selon vos besoins)
        $adminEmails = ['<EMAIL>', '<EMAIL>']; // Ajoutez les emails des admins ici

        if (!in_array($user->email, $adminEmails)) {
            return redirect()->route('home')->with('error', 'Accès non autorisé. Seuls les administrateurs peuvent accéder au tableau de bord.');
        }

        return $next($request);
    }
}
