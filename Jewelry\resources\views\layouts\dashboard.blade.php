<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>@yield('title', 'Dashboard') - MAJĀA</title>
    
    <!-- Tailwind CSS -->
    @vite('resources/css/app.css')
    
    <!-- Boxicons -->
    <link rel="stylesheet" href="https://unpkg.com/boxicons@2.1.4/css/boxicons.min.css">
    
    <!-- Custom Styles -->
    <style>
        .sidebar-link {
            @apply flex items-center gap-3 py-3 px-4 rounded-lg transition-all duration-200;
        }
        .sidebar-link:hover {
            @apply bg-[#C9B194]/10;
        }
        .sidebar-link.active {
            @apply bg-[#C9B194]/20 text-[#C9B194];
        }
        .sidebar-icon {
            @apply text-xl;
        }
        .dashboard-card {
            @apply bg-white rounded-xl shadow-sm p-6 transition-all duration-300 hover:shadow-md;
        }
        .btn-primary {
            @apply bg-[#C9B194] hover:bg-[#B89E80] text-white font-medium py-2 px-4 rounded-lg transition-all duration-300;
        }
        .btn-secondary {
            @apply bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium py-2 px-4 rounded-lg transition-all duration-300;
        }
        .btn-danger {
            @apply bg-red-500 hover:bg-red-600 text-white font-medium py-2 px-4 rounded-lg transition-all duration-300;
        }
        .form-input {
            @apply w-full px-4 py-2 border rounded-lg focus:outline-none focus:border-[#C9B194];
        }
        .form-label {
            @apply block text-gray-700 mb-2 font-medium;
        }
        .form-error {
            @apply text-red-500 text-sm mt-1;
        }
        .badge-success {
            @apply bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded-full;
        }
        .badge-danger {
            @apply bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded-full;
        }
        .notification-badge {
            @apply bg-red-500 text-white text-xs rounded-full px-2 py-1 ml-auto;
        }
    </style>
    
    @stack('styles')
</head>
<body class="bg-gray-50 text-gray-800 min-h-screen">
    <div class="flex">
        <!-- Sidebar -->
        <aside class="fixed inset-y-0 left-0 bg-white shadow-md max-h-screen w-60 hidden md:block">
            <div class="flex flex-col justify-between h-full">
                <div class="flex-grow">
                    <div class="px-4 py-6 text-center border-b">
                        <a href="{{ route('home') }}" class="flex items-center justify-center">
                            <img src="{{ asset('images/logo1.png') }}" alt="MAJĀA" class="w-32">
                        </a>
                    </div>
                    <div class="p-4">
                        <ul class="space-y-1">
                            <li>
                                <a href="{{ route('dashboard') }}" class="sidebar-link {{ request()->routeIs('dashboard') && !request()->is('*/create') && !request()->is('*/edit') && !request()->is('*/notifications') ? 'active' : '' }}">
                                    <i class="bx bxs-dashboard sidebar-icon"></i>
                                    <span>Tableau de bord</span>
                                </a>
                            </li>
                            <li>
                                <a href="{{ route('dashboard.create') }}" class="sidebar-link {{ request()->routeIs('dashboard.create') ? 'active' : '' }}">
                                    <i class="bx bx-plus-circle sidebar-icon"></i>
                                    <span>Ajouter un produit</span>
                                </a>
                            </li>
                            <li>
                                <a href="{{ route('dashboard.notifications') }}" class="sidebar-link {{ request()->routeIs('dashboard.notifications') ? 'active' : '' }}">
                                    <i class="bx bx-bell sidebar-icon"></i>
                                    <span>Notifications</span>
                                    @php
                                        $outOfStockCount = \App\Models\Product::outOfStock()->count();
                                        $preorderCount = \App\Models\ProductNotification::whereHas('product', function($query) {
                                            $query->outOfStock();
                                        })->count();
                                        $totalNotifications = $outOfStockCount + $preorderCount;
                                    @endphp
                                    @if($totalNotifications > 0)
                                        <span class="notification-badge">{{ $totalNotifications }}</span>
                                    @endif
                                </a>
                            </li>
                            <li class="border-t my-2"></li>
                            <li>
                                <a href="{{ route('home') }}" class="sidebar-link">
                                    <i class="bx bx-store sidebar-icon"></i>
                                    <span>Voir la boutique</span>
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="p-4 border-t">
                    <form action="{{ route('logout') }}" method="POST">
                        @csrf
                        <button type="submit" class="flex items-center w-full sidebar-link text-red-500 hover:bg-red-50">
                            <i class="bx bx-log-out sidebar-icon"></i>
                            <span>Déconnexion</span>
                        </button>
                    </form>
                </div>
            </div>
        </aside>

        <!-- Mobile sidebar -->
        <div class="fixed inset-0 bg-black/50 z-40 md:hidden hidden" id="sidebar-overlay"></div>
        <aside class="fixed inset-y-0 left-0 bg-white shadow-md max-h-screen w-60 md:hidden z-50 transform -translate-x-full transition-transform duration-300" id="mobile-sidebar">
            <div class="flex flex-col justify-between h-full">
                <div class="flex-grow">
                    <div class="flex items-center justify-between px-4 py-6 border-b">
                        <a href="{{ route('home') }}" class="flex items-center">
                            <img src="{{ asset('images/logo1.png') }}" alt="MAJĀA" class="w-28">
                        </a>
                        <button id="close-sidebar" class="p-2 rounded-md hover:bg-gray-100">
                            <i class="bx bx-x text-2xl"></i>
                        </button>
                    </div>
                    <div class="p-4">
                        <ul class="space-y-1">
                            <li>
                                <a href="{{ route('dashboard') }}" class="sidebar-link {{ request()->routeIs('dashboard') && !request()->is('*/create') && !request()->is('*/edit') && !request()->is('*/notifications') ? 'active' : '' }}">
                                    <i class="bx bxs-dashboard sidebar-icon"></i>
                                    <span>Tableau de bord</span>
                                </a>
                            </li>
                            <li>
                                <a href="{{ route('dashboard.create') }}" class="sidebar-link {{ request()->routeIs('dashboard.create') ? 'active' : '' }}">
                                    <i class="bx bx-plus-circle sidebar-icon"></i>
                                    <span>Ajouter un produit</span>
                                </a>
                            </li>
                            <li>
                                <a href="{{ route('dashboard.notifications') }}" class="sidebar-link {{ request()->routeIs('dashboard.notifications') ? 'active' : '' }}">
                                    <i class="bx bx-bell sidebar-icon"></i>
                                    <span>Notifications</span>
                                    @if($totalNotifications > 0)
                                        <span class="notification-badge">{{ $totalNotifications }}</span>
                                    @endif
                                </a>
                            </li>
                            <li class="border-t my-2"></li>
                            <li>
                                <a href="{{ route('home') }}" class="sidebar-link">
                                    <i class="bx bx-store sidebar-icon"></i>
                                    <span>Voir la boutique</span>
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="p-4 border-t">
                    <form action="{{ route('logout') }}" method="POST">
                        @csrf
                        <button type="submit" class="flex items-center w-full sidebar-link text-red-500 hover:bg-red-50">
                            <i class="bx bx-log-out sidebar-icon"></i>
                            <span>Déconnexion</span>
                        </button>
                    </form>
                </div>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="flex-1 ml-0 md:ml-60 min-h-screen">
            <!-- Top Bar -->
            <header class="bg-white shadow-sm sticky top-0 z-30">
                <div class="flex items-center justify-between p-4">
                    <button id="open-sidebar" class="p-2 rounded-md hover:bg-gray-100 md:hidden">
                        <i class="bx bx-menu text-2xl"></i>
                    </button>
                    <div class="text-xl font-semibold text-gray-800 md:hidden">
                        MAJĀA Admin
                    </div>
                    <div class="flex items-center space-x-4">
                        <a href="{{ route('dashboard.notifications') }}" class="relative p-2 text-gray-600 hover:text-[#C9B194]">
                            <i class="bx bx-bell text-xl"></i>
                            @if($totalNotifications > 0)
                                <span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">{{ $totalNotifications }}</span>
                            @endif
                        </a>
                        <span class="text-sm font-medium">{{ Auth::user()->name }}</span>
                    </div>
                </div>
            </header>

            <!-- Page Content -->
            <div class="p-4 sm:p-6 lg:p-8">
                @if(session('success'))
                    <div class="mb-6 p-4 bg-green-100 text-green-700 rounded-lg border border-green-200 flex items-center justify-between">
                        <div class="flex items-center">
                            <i class="bx bx-check-circle text-xl mr-2"></i>
                            <span>{{ session('success') }}</span>
                        </div>
                        <button class="text-green-700 hover:text-green-900" onclick="this.parentElement.remove()">
                            <i class="bx bx-x text-xl"></i>
                        </button>
                    </div>
                @endif

                @if(session('error'))
                    <div class="mb-6 p-4 bg-red-100 text-red-700 rounded-lg border border-red-200 flex items-center justify-between">
                        <div class="flex items-center">
                            <i class="bx bx-error-circle text-xl mr-2"></i>
                            <span>{{ session('error') }}</span>
                        </div>
                        <button class="text-red-700 hover:text-red-900" onclick="this.parentElement.remove()">
                            <i class="bx bx-x text-xl"></i>
                        </button>
                    </div>
                @endif

                @yield('content')
            </div>
        </main>
    </div>

    <!-- Scripts -->
    <script>
        // Mobile sidebar toggle
        document.getElementById('open-sidebar').addEventListener('click', function() {
            document.getElementById('mobile-sidebar').classList.remove('-translate-x-full');
            document.getElementById('sidebar-overlay').classList.remove('hidden');
        });

        document.getElementById('close-sidebar').addEventListener('click', function() {
            document.getElementById('mobile-sidebar').classList.add('-translate-x-full');
            document.getElementById('sidebar-overlay').classList.add('hidden');
        });

        document.getElementById('sidebar-overlay').addEventListener('click', function() {
            document.getElementById('mobile-sidebar').classList.add('-translate-x-full');
            document.getElementById('sidebar-overlay').classList.add('hidden');
        });
    </script>

    @stack('scripts')
</body>
</html>
