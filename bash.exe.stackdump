Stack trace:
Frame         Function      Args
0007FFFFBE20  00021006118E (00021028DEE8, 000210272B3E, 0007FFFFBE20, 0007FFFFAD20) msys-2.0.dll+0x2118E
0007FFFFBE20  0002100469BA (000000000000, 000000000000, 000000000000, 0007FFFFC0F8) msys-2.0.dll+0x69BA
0007FFFFBE20  0002100469F2 (00021028DF99, 0007FFFFBCD8, 0007FFFFBE20, 000000000000) msys-2.0.dll+0x69F2
0007FFFFBE20  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFFBE20  00021006A545 (0007FFFFBE30, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0007FFFFC100  00021006B9A5 (0007FFFFBE30, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFA227E0000 ntdll.dll
7FFA20930000 KERNEL32.DLL
7FFA1FAA0000 KERNELBASE.dll
7FFA20B00000 USER32.dll
7FFA1FE70000 win32u.dll
7FFA20CD0000 GDI32.dll
7FFA20170000 gdi32full.dll
7FFA204F0000 msvcp_win.dll
7FFA20020000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFA20EF0000 advapi32.dll
7FFA21BD0000 msvcrt.dll
7FFA226F0000 sechost.dll
7FFA21C80000 RPCRT4.dll
7FFA1EE70000 CRYPTBASE.DLL
7FFA1F970000 bcryptPrimitives.dll
7FFA20FB0000 IMM32.DLL
