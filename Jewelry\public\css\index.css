/* Fonts */
@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@100;300;400;500;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap');
/* Color Variables */
:root {
  --white: #fff;
  --black: #222;
  --green: #C9B194;
  --grey1: #f0f0f0;
  --grey2: #f1f1f1;
}

/* Basic Reset */
*,
*::before,
*::after {
  margin: 0;
  padding: 0;
  box-sizing: inherit;
}

html {
  box-sizing: border-box;
  font-size: 62.5%;
}

body {
  font-family: 'Inter', sans-serif;
  font-size: 1.6rem;
  background-color: var(--white);
  color: var(--black);
  font-weight: 400;
  font-style: normal;
}

a {
  text-decoration: none;
  color: var(--black);
}

li {
  list-style: none;
}

.container {
  max-width: 114rem;
  margin: 0 auto;
  padding: 0 1rem;
}

.d-flex {
  display: flex;
  align-items: center;
}

/* 
=================
Header
=================
*/

.header {
  position: relative;
  min-height: 100vh;
  overflow-x: hidden;
}


/* 
=================
Hero Area
=================
*/

.hero,
.hero .glide__slides {
  background-color: var(--grey2);
  position: relative;
  height: calc(100vh - 6rem);
  margin: 0 4rem;
  overflow: hidden;
}

.hero .center {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  height: 100%;
  padding-top: 3rem;
}

.hero .left {
  position: absolute;
  top: 0%;
  left: 25rem;
  opacity: 0;
  transition: all 1000ms ease-in-out;
}

.hero .left h1 {
  font-size: 5rem;
  margin: 1rem 0;
}

.hero .left p {
  font-size: 1.6rem;
  margin-bottom: 1rem;
}

.hero .left .hero-btn {
  display: inline-block;
  padding: 1rem 3rem;
  font-size: 1.6rem;
  margin-top: 1rem;
  background-color: var(--green);
  color: white;
}

.hero .right {
  flex: 0 0 50%;
  height: 100%;
  position: relative;
  text-align: center;
}

.hero .right img {
  position: absolute;
  width: 55rem;
  opacity: 0;
  transition: all 1000ms ease-in-out;
}

.center .right img.img1 {
  width: 85%;
  right: -25%;
  bottom: -50%;
}

.center .right img.img2 {
  width: 90%;
  right: -40%;
  bottom: -40%;
}

.glide__slide--active .center .right img {
  bottom: -3%;
  opacity: 1;
}

.glide__slide--active .left {
  opacity: 1;
  top: 35%;
}

/*
======================
Hero Media Query
======================
*/
@media only screen and (max-width: 1500px) {
  .hero .left {
    left: 0;
  }
}
@media only screen and (max-width: 999px) {
  .glide__slide--active .left {
    top: 15%;
  }

  .center .right img.img1 {
    width: 100%;
  }

  .center .right img.img2 {
    width: 100%;
  }
}

@media only screen and (max-width: 567px) {
  .center .right img.img1 {
    width: 40rem;
    max-width: none;
  }

  .center .right img.img2 {
    width: 40rem;
    max-width: none;
  }

  .hero,
  .hero .glide__slides {
    margin: 0;
    padding: 0 3rem;
  }

  .hero .left h1 {
    font-size: 3rem;
  }

  .hero .left span {
    font-size: 1.4rem;
  }
  .hero .left p {
    font-size: 1.5rem;
    width: 80%;
  }
}

/* Category Section */
.section {
  padding: 10rem 0 5rem 0;
  overflow: hidden;
}

.cat-center {
  display: flex;
  align-items: center;
  justify-content: center;
  max-width: 114rem;
  margin: auto;
  padding: 0 3rem;
}

.cat-center .cat {
  max-width: 37rem;
  height: 25rem;
  overflow: hidden;
  position: relative;
  cursor: pointer;
  text-align: center;
}

.cat-center .cat:not(:last-child) {
  margin-right: 3rem;
}

.cat-center .cat img {
  width: 36.5rem;
  height: 100%;
  margin: auto;
  object-fit: cover;
  transition: all 2s ease;
}

.cat-center .cat:hover img {
  transform: scale(1.1);
}

.cat-center .cat div {
  position: absolute;
  bottom: 15%;
  left: 50%;
  transform: translateX(-50%);
  background-color: var(--white);
  width: 18rem;
  height: 5rem;
  line-height: 5rem;
  text-align: center;
  font-size: 2rem;
  font-weight: 500;
}

@media only screen and (max-width: 1200px) {
  .cat-center .cat img {
    width: 30rem;
  }
}

@media only screen and (max-width: 967px) {
  .cat-center .cat {
    max-width: 25rem;
    height: 20rem;
  }

  .cat-center .cat img {
    width: 25rem;
  }
}

@media only screen and (max-width: 768px) {
  .section {
    padding: 5rem 0;
  }

  .cat-center {
    flex-direction: column;
    padding: 3rem;
  }

  .cat-center .cat {
    max-width: 100%;
    height: 30rem;
  }

  .cat-center .cat:not(:last-child) {
    margin-right: 0rem;
    margin-bottom: 5rem;
  }

  .cat-center .cat img {
    width: 100%;
  }
}

/* New Arrivals */
.title {
  text-align: center;
  margin-bottom: 5rem;
}

.title h1 {
  font-size: 3rem;
  text-transform: uppercase;
  margin-bottom: 1rem;
  font-weight: 500;
}

.product-center {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  max-width: 120rem;
  margin: 0 auto;
  padding: 0 2rem;
}

.product-item {
  position: relative;
  width: 27rem;
  margin: 0 auto;
  margin-bottom: 3rem;
}

.product-thumb img {
  width: 100%;
  height: 425px;
  object-fit: cover;
  transition: all 500ms linear;
}

.product-info {
  padding: 1rem;
  text-align: center;
}

.product-info span {
  display: inline-block;
  font-size: 1.2rem;
  color: #666;
  margin-bottom: 1rem;
}

.product-info a {
  font-size: 1.5rem;
  display: block;
  margin-bottom: 1rem;
  transition: all 300ms ease;
}

.product-info a:hover {
  color: var(--green);
}

.product-item .icons {
  position: absolute;
  left: 50%;
  top: 35%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
}

.product-item .icons li {
  background-color: var(--white);
  text-align: center;
  padding: 1rem 1.5rem;
  font-size: 2.3rem;
  cursor: pointer;
  transition: 300ms ease-out;
  transform: translateY(20px);
  visibility: hidden;
  opacity: 0;
}

.product-item .icons li:first-child {
  transition-delay: 0.1s;
}

.product-item .icons li:nth-child(2) {
  transition-delay: 0.2s;
}

.product-item .icons li:nth-child(3) {
  transition-delay: 0.3s;
}

.product-item:hover .icons li {
  visibility: visible;
  opacity: 1;
  transform: translateY(0);
}

.product-item .icons li:not(:last-child) {
  margin-right: 0.5rem;
}

.product-item .icons li:hover {
  background-color: var(--green);
  color: var(--white);
}

.product-item .overlay {
  position: relative;
  overflow: hidden;
  cursor: pointer;
  width: 100%;
}

.product-item .overlay::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  visibility: hidden;
  opacity: 0;
  transition: 300ms ease-out;
}

.product-item:hover .overlay::after {
  visibility: visible;
  opacity: 1;
}

.product-item:hover .product-thumb img {
  transform: scale(1.1);
}

.product-item .discount {
  position: absolute;
  top: 1rem;
  left: 1rem;
  background-color: var(--green);
  padding: 0.5rem 1rem;
  color: var(--white);
  border-radius: 2rem;
  font-size: 1.5rem;
}

@media only screen and (max-width: 567px) {
  .product-center {
    max-width: 100%;
    padding: 0 1rem;
  }

  .product-item {
    width: 47%;
    margin-bottom: 3rem;
  }

  .product-thumb img {
    height: 300px;
  }

  

  .product-item .icons li {
    padding: 0.5rem 1rem;
    font-size: 1.8rem;
  }
}

/* Banner */

.banner {
  position: relative;
  background-color: var(--grey2);
  padding: 5rem 20%;
}

.banner .right img {
  position: absolute;
  bottom: 0;
  right: 5%;
  width: 65rem;
}

.banner .trend,
.banner p {
  display: block;
  font-size: 2rem;
  font-weight: 300;
  margin-bottom: 1.5rem;
}

.banner h1 {
  font-size: 5.4rem;
  color: var(--black);
  margin-bottom: 1.5rem;
}

.banner .btn {
  display: inline-block;
  margin-top: 2rem;
}

@media only screen and (max-width: 1500px) {
  .banner {
    padding: 8rem 5rem;
  }

  .banner .right img {
    right: 3rem;
    width: 70rem;
  }
}

@media only screen and (max-width: 996px) {
  .banner {
    padding: 8rem 2rem;
  }

  .banner .trend,
  .banner p {
    font-size: 1.5rem;
  }

  .banner h1 {
    font-size: 4rem;
  }

  .banner .right img {
    right: -6%;
    width: 50rem;
  }
}

@media only screen and (max-width: 768px) {
  .banner {
    display: grid;
    grid-template-columns: 1fr;
    height: 80vh;
  }

  .banner .left {
    width: 100%;
    margin-bottom: 3rem;
  }

  .banner .right {
    flex: 0 0 50%;
  }

  .banner .right img {
    right: 0;
    width: 60%;
    height: 50rem;
    margin: 0 auto;
  }
}

@media only screen and (max-width: 567px) {
  .banner {
    padding: 8rem 4rem;
  }

  .banner .trend,
  .banner p {
    font-size: 1.3rem;
    margin-bottom: 1rem;
  }

  .banner h1 {
    font-size: 3rem;
    margin-bottom: 1rem;
  }

  .banner .btn {
    padding: 0.8rem 1.7rem;
    font-size: 1.4rem;
  }

  .banner .right img {
    width: 40rem;
    height: 40rem;
    
  }
}

/* Contact */
.contact {
  background-color: var(--black);
  color: var(--white);
  padding: 10rem 20rem;
}

.contact .row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  align-items: center;
}

.contact .row .col h2 {
  margin-bottom: 1.5rem;
}

.contact .row .col p {
  width: 70%;
  margin-bottom: 2rem;
}

.btn-1 {
  background-color: var(--green);
  color: var(--white);
  display: inline-block;
  border-radius: 1rem;
  padding: 1rem 2rem;
}

.contact form div {
  position: relative;
  width: 80%;
  margin: 0 auto;
}

.contact form input {
  font-family: 'Inter', sans-serif;
  text-indent: 2rem;
  width: 100%;
  border-radius: 1rem;
  padding: 1.5rem 0;
  outline: none;
  border: none;
}

.contact form a {
  position: absolute;
  top: 0;
  right: 0;
  background-color: var(--green);
  color: white;
  margin: 0.5rem;
  padding: 1rem 3rem;
  border-radius: 1rem;
}

@media only screen and (max-width: 996px) {
  .contact {
    padding: 8rem 8rem;
  }
}

@media only screen and (max-width: 768px) {
  .contact .row {
    grid-template-columns: 1fr;
    gap: 5rem 0;
  }

  .contact form div {
    width: 100%;
  }
}

@media only screen and (max-width: 567px) {
  .contact {
    padding: 8rem 1rem;
  }
}


/* Popup */
.popup {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  transition: 0.3s;
  transform: scale(1);
}

.popup-content {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 90%;
  max-width: 110rem;
  margin: 0 auto;
  height: 55rem;
  transform: translate(-50%, -50%);
  padding: 1.6rem;
  display: table;
  overflow: hidden;
  background-color: var(--white);
}

.popup-close {
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  top: 2rem;
  right: 2rem;
  padding: 0.5rem;
  background-color: var(--green);
  border-radius: 50%;
  cursor: pointer;
}

.popup-close {
  font-size: 3rem;
  color: white;
}

.popup-left {
  display: table-cell;
  width: 50%;
  height: 100%;
  vertical-align: middle;
}

.popup-right {
  display: table-cell;
  width: 50%;
  vertical-align: middle;
  padding: 3rem 5rem;
}

.popup-img-container {
  width: 100%;
  overflow: hidden;
}

.popup-img-container img.popup-img {
  display: block;
  width: 60rem;
  height: 45rem;
  max-width: 100%;
  border-radius: 1rem;
  object-fit: cover;
}

.right-content {
  text-align: center;
  width: 85%;
  margin: 0 auto;
}

.right-content h1 {
  font-size: 4rem;
  color: #000;
  margin-bottom: 1.6rem;
}

.right-content h1 span {
  color: var(--green);
}

.right-content p {
  font-size: 1.6rem;
  color: #555;
  margin-bottom: 1.6rem;
}

.popup-form {
  width: 100%;
  padding: 1.5rem 0;
  text-indent: 1rem;
  margin-bottom: 1.6rem;
  border-radius: 3rem;
  background-color: var(--green);
  color: white;
  font-size: 1.8rem;
  border: none;
}

.popup-form::placeholder {
  color: white;
}

.popup-form:focus {
  outline: none;
}

.right-content a:link,
.right-content a:visited {
  display: inline-block;
  padding: 1.8rem 5rem;
  border-radius: 3rem;
  font-weight: 700;
  color: var(--white);
  background-color: var(--black);
  border: 1px solid var(--grey2);
  transition: 0.3s;
}

.right-content a:hover {
  background-color: var(--green);
  border: 1px solid var(--grey2);
  color: var(--black);
}

.hide-popup {
  transform: scale(0.2);
  opacity: 0;
  visibility: hidden;
}

@media only screen and (max-width: 1200px) {
  .right-content {
    width: 100%;
  }

  .right-content h1 {
    font-size: 3.5rem;
    margin-bottom: 1.3rem;
  }
}

@media only screen and (max-width: 998px) {
  .popup-right {
    width: 100%;
  }

  .popup-left {
    display: none;
  }

  .right-content h1 {
    font-size: 5rem;
  }
}

@media only screen and (max-width: 768px) {
  .right-content h1 {
    font-size: 4rem;
  }

  .right-content p {
    font-size: 1.6rem;
  }

  .popup-form {
    width: 90%;
    margin: 0 auto;
    padding: 1.8rem 0;
    margin-bottom: 1.5rem;
  }

  .goto-top:link,
  .goto-top:visited {
    right: 5%;
    bottom: 5%;
  }
}

@media only screen and (max-width: 568px) {
  .popup-right {
    padding: 0 1.6rem;
  }

  .popup-content {
    height: 35rem;
    width: 90%;
    margin: 0 auto;
  }

  .right-content {
    width: 100%;
  }

  .right-content h1 {
    font-size: 3rem;
  }

  .right-content p {
    font-size: 1.4rem;
  }

  .popup-form {
    width: 100%;
    padding: 1.5rem 0;
    margin-bottom: 1.3rem;
  }

  .right-content a:link,
  .right-content a:visited {
    padding: 1.5rem 3rem;
  }

  .popup-close {
    top: 1rem;
    right: 1rem;
    padding: 0.5rem;
  }
}

/* All Products */
.section .top {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 4rem;
}

.all-products .top select {
  font-family: 'Poppins', sans-serif;
  width: 20rem;
  padding: 1rem;
  border: 1px solid #ccc;
  appearance: none;
  outline: none;
}

form {
  position: relative;
  z-index: 1;
}

form span {
  position: absolute;
  top: 50%;
  right: 1rem;
  transform: translateY(-50%);
  font-size: 2rem;
  z-index: 0;
}

@media only screen and (max-width: 768px) {
  .all-products .top select {
    width: 15rem;
  }
}

/* Pagination */
.pagination {
  display: flex;
  align-items: center;
  padding: 3rem 0 5rem 0;
}

.pagination span {
  display: inline-block;
  padding: 1rem 1.5rem;
  border: 1px solid var(--green);
  font-size: 1.8rem;
  margin-bottom: 2rem;
  cursor: pointer;
  transition: all 300ms ease-in-out;
}

.pagination span:hover {
  border: 1px solid var(--green);
  background-color: var(--green);
  color: #fff;
}

/* Cart Items */
.cart {
  margin: 10rem auto;
}

table {
  width: 100%;
  border-collapse: collapse;
}

.cart-info {
  display: flex;
  flex-wrap: wrap;
}

th {
  text-align: left;
  padding: 0.5rem;
  color: #fff;
  background-color: var(--green);
  font-weight: normal;
}

td {
  padding: 1rem 0.5rem;
}

td input {
  width: 5rem;
  height: 3rem;
  padding: 0.5rem;
}

td a {
  color: orangered;
  font-size: 1.4rem;
}

td img {
  width: 8rem;
  height: 8rem;
  margin-right: 1rem;
}

.total-price {
  display: flex;
  align-items: flex-end;
  flex-direction: column;
  margin-top: 2rem;
}

.total-price table {
  border-top: 3px solid var(--green);
  width: 100%;
  max-width: 35rem;
}

td:last-child {
  text-align: right;
}

th:last-child {
  text-align: right;
}

.checkout {
  display: inline-block;
  background-color: var(--green);
  color: white;
  padding: 1rem;
  margin-top: 1rem;
}

@media only screen and (max-width: 567px) {
  .cart-info p {
    display: none;
  }
}

/* Product Details */
.product-detail .details {
  display: grid;
  grid-template-columns: 1fr 1.2fr;
  gap: 7rem;
}

.product-detail .left {
  display: flex;
  flex-direction: column;
}

.product-detail .left .main {
  text-align: center;
  background-color: #f6f2f1;
  margin-bottom: 2rem;
  height: 45rem;
}

.product-detail .left .main img {
  object-fit: cover;
  height: 100%;
  width: 100%;
}

.product-detail .right span {
  display: inline-block;
  font-size: 1.5rem;
  margin-bottom: 1rem;
}

.product-detail .right h1 {
  font-size: 4rem;
  line-height: 1.2;
  margin-bottom: 2rem;
}

.product-detail .right .price {
  font-size: 600;
  font-size: 2rem;
  color: var(--green);
  margin-bottom: 2rem;
}

.product-detail .right div {
  display: inline-block;
  position: relative;
  z-index: -1;
}

.product-detail .right select {
  font-family: 'Poppins', sans-serif;
  width: 20rem;
  padding: 0.7rem;
  border: 1px solid #ccc;
  appearance: none;
  outline: none;
}

.product-detail form {
  margin-bottom: 2rem;
  z-index: 0;
}

.product-detail form span {
  position: absolute;
  top: 50%;
  right: 1rem;
  transform: translateY(-50%);
  font-size: 2rem;
  z-index: 0;
}

.product-detail .form {
  margin-bottom: 3rem;
}

.product-detail .form input {
  padding: 0.8rem;
  text-align: center;
  width: 3.5rem;
  margin-right: 2rem;
}

.product-detail .form .addCart {
  background: var(--green);
  padding: 0.8rem 4rem;
  color: #fff;
  border-radius: 3rem;
}

.product-detail h3 {
  text-transform: uppercase;
  margin-bottom: 2rem;
}

@media only screen and (max-width: 996px) {
  .product-detail .left {
    width: 30rem;
    height: 45rem;
  }

  .product-detail .details {
    gap: 3rem;
  }
}

@media only screen and (max-width: 650px) {
  .product-detail .details {
    grid-template-columns: 1fr;
  }

  .product-detail .right {
    margin-top: 1rem;
  }

  .product-detail .left {
    width: 100%;
    height: 45rem;
  }

  .product-detail .details {
    gap: 3rem;
  }
}

/* Login Form */
.login-form {
  padding: 5rem 0;
  max-width: 50rem;
  margin: 5rem auto;
}

.login-form form {
  display: flex;
  flex-direction: column;
}

.login-form form h1 {
  margin-bottom: 1rem;
}
.login-form form p {
  margin-bottom: 2rem;
}

.login-form form label {
  margin-bottom: 1rem;
}

.login-form form input {
  border: 1px solid #ccc;
  outline: none;
  padding: 1.5rem 0;
  text-indent: 1rem;
  font-size: 1.6rem;
  margin-bottom: 2rem;
  border-radius: 0.5rem;
}

.login-form form input::placeholder {
  font-size: 1.6rem;
  color: #222;
}

.login-form form .buttons {
  margin-top: 1rem;
}

.login-form form button {
  outline: none;
  border: none;
  font-size: 1.6rem;
  padding: 1rem 3rem;
  margin-right: 1.5rem;
  background-color: var(--green);
  color: white;
  cursor: pointer;
}

.login-form form p a {
  color: var(--green);
}