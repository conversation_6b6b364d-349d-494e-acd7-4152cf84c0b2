@extends('layouts.dashboard')

@section('title', 'Tableau de bord')

@section('content')
    <div class="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <h1 class="text-2xl font-bold text-gray-900">Tableau de bord</h1>
        <div class="mt-4 sm:mt-0">
            <a href="{{ route('dashboard.create') }}" class="btn-primary flex items-center">
                <i class="bx bx-plus mr-2"></i>
                Ajouter un produit
            </a>
        </div>
    </div>

    <!-- Statistiques rapides -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="dashboard-card">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-blue-100">
                    <i class="bx bx-package text-2xl text-blue-600"></i>
                </div>
                <div class="ml-4">
                    <h3 class="text-lg font-semibold text-gray-900">{{ $products->total() }}</h3>
                    <p class="text-sm text-gray-600">Total produits</p>
                </div>
            </div>
        </div>

        <div class="dashboard-card">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-green-100">
                    <i class="bx bx-check-circle text-2xl text-green-600"></i>
                </div>
                <div class="ml-4">
                    <h3 class="text-lg font-semibold text-gray-900">{{ $products->total() - $outOfStockProducts }}</h3>
                    <p class="text-sm text-gray-600">Produits actifs</p>
                </div>
            </div>
        </div>

        <div class="dashboard-card">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-red-100">
                    <i class="bx bx-x-circle text-2xl text-red-600"></i>
                </div>
                <div class="ml-4">
                    <h3 class="text-lg font-semibold text-gray-900">{{ $outOfStockProducts }}</h3>
                    <p class="text-sm text-gray-600">Ruptures de stock</p>
                </div>
            </div>
        </div>

        <div class="dashboard-card">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-yellow-100">
                    <i class="bx bx-bell text-2xl text-yellow-600"></i>
                </div>
                <div class="ml-4">
                    <h3 class="text-lg font-semibold text-gray-900">{{ $preorderNotifications }}</h3>
                    <p class="text-sm text-gray-600">Précommandes</p>
                </div>
            </div>
        </div>
    </div>

    @if($totalNotifications > 0)
        <div class="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <div class="flex items-center">
                <i class="bx bx-info-circle text-yellow-600 text-xl mr-3"></i>
                <div>
                    <h3 class="font-medium text-yellow-800">Attention requise</h3>
                    <p class="text-sm text-yellow-700 mt-1">
                        Vous avez {{ $totalNotifications }} notification(s) en attente. 
                        <a href="{{ route('dashboard.notifications') }}" class="underline hover:no-underline">Voir les notifications</a>
                    </p>
                </div>
            </div>
        </div>
    @endif

    <div class="dashboard-card overflow-hidden">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between pb-4 border-b">
            <h2 class="text-lg font-medium">Liste des produits</h2>
            <div class="mt-3 sm:mt-0 flex items-center space-x-2">
                <span class="text-sm text-gray-500">{{ $products->total() }} produits</span>
            </div>
        </div>

        <div class="overflow-x-auto">
            <table class="w-full text-sm text-left">
                <thead class="text-xs text-gray-700 uppercase bg-gray-50">
                    <tr>
                        <th class="px-4 py-3">Image</th>
                        <th class="px-4 py-3">Nom</th>
                        <th class="px-4 py-3">Catégorie</th>
                        <th class="px-4 py-3">Prix</th>
                        <th class="px-4 py-3">Statut</th>
                        <th class="px-4 py-3">Stock</th>
                        <th class="px-4 py-3">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse ($products as $product)
                        <tr class="border-b hover:bg-gray-50 {{ $product->is_out_of_stock ? 'bg-red-50' : '' }}">
                            <td class="px-4 py-3">
                                <img src="{{ asset('storage/' . $product->image) }}" alt="{{ $product->name }}" class="w-16 h-16 object-cover rounded">
                            </td>
                            <td class="px-4 py-3 font-medium">
                                {{ $product->name }}
                                @if($product->is_out_of_stock)
                                    <i class="bx bx-error-circle text-red-500 ml-1" title="Rupture de stock"></i>
                                @endif
                            </td>
                            <td class="px-4 py-3">{{ $product->category }}</td>
                            <td class="px-4 py-3">{{ number_format($product->price, 2) }}€</td>
                            <td class="px-4 py-3">
                                <form action="{{ route('dashboard.toggle-active', $product->id) }}" method="POST" class="inline">
                                    @csrf
                                    @method('PATCH')
                                    <button type="submit" class="{{ $product->is_active ? 'badge-success' : 'badge-danger' }}">
                                        {{ $product->is_active ? 'Actif' : 'Inactif' }}
                                    </button>
                                </form>
                            </td>
                            <td class="px-4 py-3">
                                <form action="{{ route('dashboard.toggle-stock', $product->id) }}" method="POST" class="inline">
                                    @csrf
                                    @method('PATCH')
                                    <button type="submit" class="{{ $product->stock ? 'badge-success' : 'badge-danger' }}">
                                        {{ $product->stock ? 'En stock' : 'Épuisé' }}
                                    </button>
                                </form>
                            </td>
                            <td class="px-4 py-3">
                                <div class="flex items-center space-x-2">
                                    <a href="{{ route('dashboard.show', $product->id) }}" class="text-blue-600 hover:text-blue-900" title="Voir">
                                        <i class="bx bx-show text-xl"></i>
                                    </a>
                                    <a href="{{ route('dashboard.edit', $product->id) }}" class="text-yellow-600 hover:text-yellow-900" title="Modifier">
                                        <i class="bx bx-edit text-xl"></i>
                                    </a>
                                    <form action="{{ route('dashboard.destroy', $product->id) }}" method="POST" class="inline delete-form">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="text-red-600 hover:text-red-900" title="Supprimer">
                                            <i class="bx bx-trash text-xl"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="7" class="px-4 py-6 text-center text-gray-500">
                                Aucun produit trouvé. <a href="{{ route('dashboard.create') }}" class="text-[#C9B194] hover:underline">Ajouter un produit</a>
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

        <div class="px-4 py-3">
            {{ $products->links() }}
        </div>
    </div>
@endsection

@push('scripts')
<script>
    // Confirmation dialog for delete
    document.querySelectorAll('.delete-form').forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            if (confirm('Êtes-vous sûr de vouloir supprimer ce produit ? Cette action est irréversible.')) {
                this.submit();
            }
        });
    });
</script>
@endpush
